const fs = require('fs');
const path = require('path');

// 创建简单的占位图标
function createPlaceholderIcon(filePath, size = 32) {
    const canvas = require('canvas');
    const { createCanvas } = canvas;
    
    const canvasElement = createCanvas(size, size);
    const ctx = canvasElement.getContext('2d');
    
    // 绘制简单的图标
    ctx.fillStyle = '#667eea';
    ctx.fillRect(0, 0, size, size);
    
    ctx.fillStyle = '#ffffff';
    ctx.font = `${size/2}px Arial`;
    ctx.textAlign = 'center';
    ctx.fillText('SS', size/2, size/2 + size/8);
    
    // 保存为PNG
    const buffer = canvasElement.toBuffer('image/png');
    fs.writeFileSync(filePath, buffer);
}

// 检查并创建图标文件
function setupIcons() {
    const assetsDir = path.join(__dirname, '../assets');
    
    if (!fs.existsSync(assetsDir)) {
        fs.mkdirSync(assetsDir, { recursive: true });
    }
    
    const icons = [
        { name: 'icon.png', size: 256 },
        { name: 'tray-icon.png', size: 32 }
    ];
    
    icons.forEach(icon => {
        const iconPath = path.join(assetsDir, icon.name);
        if (!fs.existsSync(iconPath) || fs.statSync(iconPath).size < 100) {
            console.log(`创建占位图标: ${icon.name}`);
            try {
                createPlaceholderIcon(iconPath, icon.size);
            } catch (error) {
                console.warn(`无法创建图标 ${icon.name}:`, error.message);
                // 创建一个简单的文本文件作为占位符
                fs.writeFileSync(iconPath, `# 占位图标文件 ${icon.name}`);
            }
        }
    });
    
    // 创建 ICO 文件占位符
    const icoPath = path.join(assetsDir, 'icon.ico');
    if (!fs.existsSync(icoPath)) {
        fs.writeFileSync(icoPath, '# ICO 图标占位符');
    }
}

if (require.main === module) {
    setupIcons();
    console.log('图标设置完成！');
}

module.exports = { setupIcons };
