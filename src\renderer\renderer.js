const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// 应用状态
let currentServer = null;
let isConnected = false;
let connectionStartTime = null;
let connectionTimer = null;
let servers = [];
let config = {};

// DOM 元素
const elements = {
    // 导航
    navLinks: document.querySelectorAll('.nav-link'),
    pages: document.querySelectorAll('.page'),
    
    // 仪表板
    statusDot: document.getElementById('statusDot'),
    statusText: document.getElementById('statusText'),
    connectBtn: document.getElementById('connectBtn'),
    disconnectBtn: document.getElementById('disconnectBtn'),
    serverInfo: document.getElementById('serverInfo'),
    currentServerName: document.getElementById('currentServerName'),
    currentServerHost: document.getElementById('currentServerHost'),
    currentServerPort: document.getElementById('currentServerPort'),
    currentServerMethod: document.getElementById('currentServerMethod'),
    uploadStats: document.getElementById('uploadStats'),
    downloadStats: document.getElementById('downloadStats'),
    connectionTime: document.getElementById('connectionTime'),
    latency: document.getElementById('latency'),
    
    // 服务器管理
    addServerBtn: document.getElementById('addServerBtn'),
    serversList: document.getElementById('serversList'),
    addServerModal: document.getElementById('addServerModal'),
    closeModal: document.getElementById('closeModal'),
    serverForm: document.getElementById('serverForm'),
    cancelBtn: document.getElementById('cancelAdd'),
    
    // 设置
    autoProxy: document.getElementById('autoProxy'),
    localPort: document.getElementById('localPort'),
    pacPort: document.getElementById('pacPort'),
    autoStart: document.getElementById('autoStart'),
    minimizeToTray: document.getElementById('minimizeToTray'),
    autoConnect: document.getElementById('autoConnect'),
    importConfigBtn: document.getElementById('importConfigBtn'),
    exportConfigBtn: document.getElementById('exportConfigBtn'),
    resetConfigBtn: document.getElementById('resetConfigBtn'),
    
    // 日志
    clearLogsBtn: document.getElementById('clearLogsBtn'),
    exportLogsBtn: document.getElementById('exportLogsBtn'),
    logOutput: document.getElementById('logOutput')
};

// 初始化应用
async function initApp() {
    await loadConfig();
    await loadServers();
    setupEventListeners();
    updateUI();
    addLog('应用启动完成');
}

// 加载配置
async function loadConfig() {
    try {
        config = await ipcRenderer.invoke('get-config');
        
        // 设置默认值
        config = {
            autoProxy: false,
            localPort: 1080,
            pacPort: 1090,
            autoStart: false,
            minimizeToTray: true,
            autoConnect: false,
            ...config
        };
        
        // 更新UI
        if (elements.autoProxy) elements.autoProxy.checked = config.autoProxy;
        if (elements.localPort) elements.localPort.value = config.localPort;
        if (elements.pacPort) elements.pacPort.value = config.pacPort;
        if (elements.autoStart) elements.autoStart.checked = config.autoStart;
        if (elements.minimizeToTray) elements.minimizeToTray.checked = config.minimizeToTray;
        if (elements.autoConnect) elements.autoConnect.checked = config.autoConnect;
    } catch (error) {
        addLog('加载配置失败: ' + error.message, 'error');
    }
}

// 保存配置
async function saveConfig() {
    try {
        config.autoProxy = elements.autoProxy.checked;
        config.localPort = parseInt(elements.localPort.value);
        config.pacPort = parseInt(elements.pacPort.value);
        config.autoStart = elements.autoStart.checked;
        config.minimizeToTray = elements.minimizeToTray.checked;
        config.autoConnect = elements.autoConnect.checked;
        
        await ipcRenderer.invoke('set-config', config);
        addLog('配置保存成功');
    } catch (error) {
        addLog('保存配置失败: ' + error.message, 'error');
    }
}

// 加载服务器列表
async function loadServers() {
    try {
        servers = await ipcRenderer.invoke('get-servers');
        updateServersList();
    } catch (error) {
        addLog('加载服务器列表失败: ' + error.message, 'error');
    }
}

// 保存服务器列表
async function saveServers() {
    try {
        await ipcRenderer.invoke('set-servers', servers);
        addLog('服务器列表保存成功');
    } catch (error) {
        addLog('保存服务器列表失败: ' + error.message, 'error');
    }
}

// 设置事件监听器
function setupEventListeners() {
    // 导航
    elements.navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const page = link.dataset.page;
            showPage(page);
        });
    });

    // 连接控制
    if (elements.connectBtn) elements.connectBtn.addEventListener('click', connect);
    if (elements.disconnectBtn) elements.disconnectBtn.addEventListener('click', disconnect);

    // 服务器管理
    if (elements.addServerBtn) elements.addServerBtn.addEventListener('click', showAddServerModal);
    if (elements.closeModal) elements.closeModal.addEventListener('click', hideAddServerModal);
    if (elements.cancelBtn) elements.cancelBtn.addEventListener('click', hideAddServerModal);
    if (elements.serverForm) elements.serverForm.addEventListener('submit', addServer);

    // 设置
    if (elements.autoProxy) elements.autoProxy.addEventListener('change', saveConfig);
    if (elements.localPort) elements.localPort.addEventListener('change', saveConfig);
    if (elements.pacPort) elements.pacPort.addEventListener('change', saveConfig);
    if (elements.autoStart) elements.autoStart.addEventListener('change', saveConfig);
    if (elements.minimizeToTray) elements.minimizeToTray.addEventListener('change', saveConfig);
    if (elements.autoConnect) elements.autoConnect.addEventListener('change', saveConfig);

    if (elements.importConfigBtn) elements.importConfigBtn.addEventListener('click', importConfig);
    if (elements.exportConfigBtn) elements.exportConfigBtn.addEventListener('click', exportConfig);
    if (elements.resetConfigBtn) elements.resetConfigBtn.addEventListener('click', resetConfig);

    // 日志
    if (elements.clearLogsBtn) elements.clearLogsBtn.addEventListener('click', clearLogs);
    if (elements.exportLogsBtn) elements.exportLogsBtn.addEventListener('click', exportLogs);

    // 模态框点击外部关闭
    if (elements.addServerModal) {
        elements.addServerModal.addEventListener('click', (e) => {
            if (e.target === elements.addServerModal) {
                hideAddServerModal();
            }
        });
    }
}

// 显示页面
function showPage(pageId) {
    elements.pages.forEach(page => page.classList.remove('active'));
    elements.navLinks.forEach(link => link.classList.remove('active'));
    
    document.getElementById(pageId).classList.add('active');
    document.querySelector(`[data-page="${pageId}"]`).classList.add('active');
}

// 连接到服务器
async function connect() {
    if (!currentServer) {
        addLog('请先选择一个服务器', 'error');
        return;
    }

    try {
        elements.connectBtn.disabled = true;
        elements.statusDot.className = 'status-dot connecting';
        elements.statusText.textContent = '连接中...';

        addLog(`正在连接到服务器: ${currentServer.name}`);

        // 实际连接到 Shadowsocks 服务器
        const result = await ipcRenderer.invoke('connect-shadowsocks', currentServer);

        if (result.success) {
            isConnected = true;
            connectionStartTime = Date.now();

            elements.statusDot.className = 'status-dot connected';
            elements.statusText.textContent = '已连接';
            elements.connectBtn.disabled = false;
            elements.disconnectBtn.disabled = false;

            // 启动连接时间计时器
            startConnectionTimer();

            // 更新托盘状态
            ipcRenderer.send('update-tray-status', 'connected');

            addLog(`成功连接到服务器: ${currentServer.name}`);
        } else {
            throw new Error(result.error || '连接失败');
        }

    } catch (error) {
        elements.connectBtn.disabled = false;
        elements.statusDot.className = 'status-dot disconnected';
        elements.statusText.textContent = '连接失败';
        addLog('连接失败: ' + error.message, 'error');
    }
}

// 断开连接
async function disconnect() {
    try {
        elements.disconnectBtn.disabled = true;

        addLog('正在断开连接...');

        // 实际断开 Shadowsocks 连接
        const result = await ipcRenderer.invoke('disconnect-shadowsocks');

        if (result.success) {
            isConnected = false;
            connectionStartTime = null;

            elements.statusDot.className = 'status-dot disconnected';
            elements.statusText.textContent = '未连接';
            elements.connectBtn.disabled = false;
            elements.disconnectBtn.disabled = true;

            // 停止连接时间计时器
            stopConnectionTimer();

            // 更新托盘状态
            ipcRenderer.send('update-tray-status', 'disconnected');

            addLog('已断开连接');
        } else {
            throw new Error(result.error || '断开连接失败');
        }

    } catch (error) {
        elements.disconnectBtn.disabled = false;
        addLog('断开连接失败: ' + error.message, 'error');
    }
}

// 启动连接时间计时器
function startConnectionTimer() {
    connectionTimer = setInterval(() => {
        if (connectionStartTime) {
            const elapsed = Date.now() - connectionStartTime;
            const hours = Math.floor(elapsed / 3600000);
            const minutes = Math.floor((elapsed % 3600000) / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            
            elements.connectionTime.textContent = 
                `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    }, 1000);
}

// 停止连接时间计时器
function stopConnectionTimer() {
    if (connectionTimer) {
        clearInterval(connectionTimer);
        connectionTimer = null;
    }
    elements.connectionTime.textContent = '00:00:00';
}

// 显示添加服务器模态框
function showAddServerModal() {
    elements.addServerModal.style.display = 'block';
}

// 隐藏添加服务器模态框
function hideAddServerModal() {
    elements.addServerModal.style.display = 'none';
    elements.serverForm.reset();
}

// 添加服务器
async function addServer(e) {
    e.preventDefault();
    
    const formData = new FormData(elements.serverForm);
    const server = {
        id: Date.now().toString(),
        name: formData.get('serverName') || document.getElementById('serverName').value,
        host: formData.get('serverHost') || document.getElementById('serverHost').value,
        port: parseInt(formData.get('serverPort') || document.getElementById('serverPort').value),
        password: formData.get('serverPassword') || document.getElementById('serverPassword').value,
        method: formData.get('serverMethod') || document.getElementById('serverMethod').value
    };
    
    servers.push(server);
    await saveServers();
    updateServersList();
    hideAddServerModal();
    
    addLog(`添加服务器: ${server.name}`);
}

// 更新服务器列表
function updateServersList() {
    elements.serversList.innerHTML = '';
    
    if (servers.length === 0) {
        elements.serversList.innerHTML = '<p style="text-align: center; padding: 40px; color: #666;">暂无服务器，请添加服务器</p>';
        return;
    }
    
    servers.forEach(server => {
        const serverItem = document.createElement('div');
        serverItem.className = 'server-item';
        serverItem.innerHTML = `
            <div class="server-details">
                <h4>${server.name}</h4>
                <p>${server.host}:${server.port} (${server.method})</p>
            </div>
            <div class="server-actions">
                <button class="btn btn-primary" onclick="selectServer('${server.id}')">选择</button>
                <button class="btn btn-danger" onclick="deleteServer('${server.id}')">删除</button>
            </div>
        `;
        elements.serversList.appendChild(serverItem);
    });
}

// 选择服务器
function selectServer(serverId) {
    currentServer = servers.find(s => s.id === serverId);
    if (currentServer) {
        elements.currentServerName.textContent = currentServer.name;
        elements.currentServerHost.textContent = currentServer.host;
        elements.currentServerPort.textContent = currentServer.port;
        elements.currentServerMethod.textContent = currentServer.method;
        elements.serverInfo.style.display = 'block';

        addLog(`选择服务器: ${currentServer.name}`);

        // 测试服务器连接
        testServerConnection(currentServer);
    }
}

// 测试服务器连接
async function testServerConnection(server) {
    try {
        addLog(`正在测试服务器连接: ${server.name}`);
        const result = await ipcRenderer.invoke('test-server', server);

        if (result.success) {
            elements.latency.textContent = `${result.latency} ms`;
            addLog(`服务器连接测试成功，延迟: ${result.latency}ms`);
        } else {
            elements.latency.textContent = '超时';
            addLog(`服务器连接测试失败: ${result.error}`, 'error');
        }
    } catch (error) {
        elements.latency.textContent = '错误';
        addLog(`服务器连接测试出错: ${error.message}`, 'error');
    }
}

// 删除服务器
async function deleteServer(serverId) {
    if (confirm('确定要删除这个服务器吗？')) {
        servers = servers.filter(s => s.id !== serverId);
        await saveServers();
        updateServersList();
        
        // 如果删除的是当前选择的服务器，清空选择
        if (currentServer && currentServer.id === serverId) {
            currentServer = null;
            elements.serverInfo.style.display = 'none';
        }
        
        addLog('服务器已删除');
    }
}

// 导入配置
function importConfig() {
    // 这个功能将通过主进程的文件对话框实现
    addLog('导入配置功能开发中...');
}

// 导出配置
function exportConfig() {
    // 这个功能将通过主进程的文件对话框实现
    addLog('导出配置功能开发中...');
}

// 重置配置
async function resetConfig() {
    if (confirm('确定要重置所有配置吗？这将清除所有服务器和设置。')) {
        config = {
            autoProxy: false,
            localPort: 1080,
            pacPort: 1090,
            autoStart: false,
            minimizeToTray: true,
            autoConnect: false
        };
        servers = [];
        
        await saveConfig();
        await saveServers();
        await loadConfig();
        updateServersList();
        
        addLog('配置已重置');
    }
}

// 清空日志
function clearLogs() {
    elements.logOutput.innerHTML = '';
}

// 导出日志
function exportLogs() {
    const logs = elements.logOutput.textContent;
    const blob = new Blob([logs], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `shadowsocks-logs-${new Date().toISOString().slice(0, 10)}.txt`;
    a.click();
    URL.revokeObjectURL(url);
}

// 添加日志
function addLog(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry log-${type}`;
    logEntry.innerHTML = `<span class="log-time">[${timestamp}]</span> ${message}`;
    
    elements.logOutput.appendChild(logEntry);
    elements.logOutput.scrollTop = elements.logOutput.scrollHeight;
}

// 更新UI
function updateUI() {
    // 根据连接状态更新UI
    if (isConnected) {
        elements.statusDot.className = 'status-dot connected';
        elements.statusText.textContent = '已连接';
        elements.connectBtn.disabled = false;
        elements.disconnectBtn.disabled = false;
    } else {
        elements.statusDot.className = 'status-dot disconnected';
        elements.statusText.textContent = '未连接';
        elements.connectBtn.disabled = false;
        elements.disconnectBtn.disabled = true;
    }
}

// 监听主进程消息
ipcRenderer.on('navigate-to', (event, page) => {
    showPage(page);
});

ipcRenderer.on('import-config', (event, filePath) => {
    addLog(`导入配置文件: ${filePath}`);
    // 实现配置导入逻辑
});

ipcRenderer.on('export-config', (event, filePath) => {
    addLog(`导出配置到: ${filePath}`);
    // 实现配置导出逻辑
});

// 监听 Shadowsocks 事件
ipcRenderer.on('shadowsocks-error', (event, error) => {
    addLog(`Shadowsocks 错误: ${error}`, 'error');

    // 如果连接出错，更新状态
    if (isConnected) {
        isConnected = false;
        connectionStartTime = null;

        elements.statusDot.className = 'status-dot disconnected';
        elements.statusText.textContent = '连接中断';
        elements.connectBtn.disabled = false;
        elements.disconnectBtn.disabled = true;

        stopConnectionTimer();
        ipcRenderer.send('update-tray-status', 'disconnected');
    }
});

ipcRenderer.on('shadowsocks-stats', (event, stats) => {
    // 更新流量统计
    elements.uploadStats.textContent = formatBytes(stats.upload);
    elements.downloadStats.textContent = formatBytes(stats.download);
});

// 格式化字节数
function formatBytes(bytes) {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 全局函数（供HTML中的onclick使用）
window.selectServer = selectServer;
window.deleteServer = deleteServer;

// 应用启动
document.addEventListener('DOMContentLoaded', initApp);
