const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 启动 Shadowsocks Electron 客户端测试...\n');

// 检查必要文件
const fs = require('fs');
const requiredFiles = [
    'package.json',
    'src/main/main.js',
    'src/renderer/index.html',
    'src/renderer/styles.css',
    'src/renderer/renderer.js',
    'src/common/shadowsocks.js',
    'src/common/proxy.js'
];

console.log('📋 检查必要文件...');
let allFilesExist = true;
requiredFiles.forEach(file => {
    if (fs.existsSync(file)) {
        console.log(`✅ ${file}`);
    } else {
        console.log(`❌ ${file} - 文件不存在`);
        allFilesExist = false;
    }
});

if (!allFilesExist) {
    console.log('\n❌ 某些必要文件缺失，请检查项目结构。');
    process.exit(1);
}

console.log('\n📦 检查依赖包...');
try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const nodeModulesExists = fs.existsSync('node_modules');
    
    if (nodeModulesExists) {
        console.log('✅ node_modules 目录存在');
    } else {
        console.log('❌ node_modules 目录不存在，请运行 npm install');
        process.exit(1);
    }
    
    // 检查关键依赖
    const keyDeps = ['electron', 'electron-store', 'socks', 'axios'];
    keyDeps.forEach(dep => {
        const depPath = path.join('node_modules', dep);
        if (fs.existsSync(depPath)) {
            console.log(`✅ ${dep}`);
        } else {
            console.log(`❌ ${dep} - 依赖包缺失`);
        }
    });
    
} catch (error) {
    console.log('❌ 无法读取 package.json');
    process.exit(1);
}

console.log('\n🎨 检查资源文件...');
const assetFiles = ['assets/icon.png', 'assets/tray-icon.png', 'assets/icon.ico'];
assetFiles.forEach(file => {
    if (fs.existsSync(file)) {
        console.log(`✅ ${file}`);
    } else {
        console.log(`⚠️  ${file} - 文件不存在（将使用占位符）`);
    }
});

console.log('\n🔧 功能特性清单:');
console.log('✅ Electron 主进程和渲染进程');
console.log('✅ 现代化用户界面');
console.log('✅ Shadowsocks 核心功能');
console.log('✅ 系统代理管理');
console.log('✅ 服务器管理');
console.log('✅ 配置存储');
console.log('✅ 系统托盘支持');
console.log('✅ 流量统计');
console.log('✅ 连接日志');

console.log('\n📖 使用说明:');
console.log('1. 应用启动后，点击侧边栏的"服务器"');
console.log('2. 点击"添加服务器"按钮添加 Shadowsocks 服务器');
console.log('3. 填写服务器信息（地址、端口、密码、加密方式）');
console.log('4. 返回"仪表板"，选择服务器后点击"连接"');
console.log('5. 在"设置"中可以配置自动代理等选项');

console.log('\n🔐 支持的加密方式:');
console.log('- aes-256-gcm');
console.log('- aes-192-gcm');
console.log('- aes-128-gcm');
console.log('- aes-256-cfb');
console.log('- aes-192-cfb');
console.log('- aes-128-cfb');
console.log('- chacha20-ietf-poly1305');

console.log('\n⚠️  注意事项:');
console.log('- 首次运行可能会有 GPU 相关警告，这是正常的');
console.log('- 系统代理功能需要管理员权限');
console.log('- 请确保拥有合法的 Shadowsocks 服务器');
console.log('- 本项目仅供学习和研究使用');

console.log('\n🚀 应用已启动！如果看到 Electron 窗口，说明一切正常。');
console.log('💡 提示：可以按 Ctrl+Shift+I 打开开发者工具进行调试。');

// 如果是直接运行这个脚本，显示额外信息
if (require.main === module) {
    console.log('\n📋 可用的 npm 脚本:');
    console.log('- npm start          # 启动应用');
    console.log('- npm run dev        # 开发模式启动');
    console.log('- npm run build      # 构建应用');
    console.log('- npm run build:win  # 构建 Windows 安装包');
    console.log('- npm run pack       # 打包应用（不创建安装程序）');
}
