const { app, BrowserWindow, Menu, Tray, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const Store = require('electron-store');
const ShadowsocksClient = require('../common/shadowsocks');
const ProxyManager = require('../common/proxy');

// 配置存储
const store = new Store();

let mainWindow;
let tray;
let isQuiting = false;
let shadowsocksClient = null;
let proxyManager = null;

// 创建主窗口
function createMainWindow() {
  const fs = require('fs');
  const iconPath = path.join(__dirname, '../../assets/icon.png');

  mainWindow = new BrowserWindow({
    width: 1000,
    height: 700,
    minWidth: 800,
    minHeight: 600,
    icon: fs.existsSync(iconPath) ? iconPath : undefined,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    show: false,
    titleBarStyle: 'default'
  });

  // 加载渲染进程页面
  mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));

  // 窗口准备好后显示
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // 窗口关闭事件
  mainWindow.on('close', (event) => {
    if (!isQuiting) {
      event.preventDefault();
      mainWindow.hide();
    }
  });

  // 开发环境下打开开发者工具
  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools();
  }
}

// 创建系统托盘
function createTray() {
  const iconPath = path.join(__dirname, '../../assets/tray-icon.png');
  const fs = require('fs');

  // 检查图标文件是否存在
  if (!fs.existsSync(iconPath)) {
    console.warn('托盘图标文件不存在，跳过托盘创建');
    return;
  }

  try {
    tray = new Tray(iconPath);
  } catch (error) {
    console.warn('创建托盘失败:', error.message);
    return;
  }

  const contextMenu = Menu.buildFromTemplate([
    {
      label: '显示主窗口',
      click: () => {
        mainWindow.show();
      }
    },
    {
      label: '连接状态',
      submenu: [
        {
          label: '已断开',
          type: 'radio',
          checked: true,
          id: 'disconnected'
        },
        {
          label: '已连接',
          type: 'radio',
          checked: false,
          id: 'connected'
        }
      ]
    },
    { type: 'separator' },
    {
      label: '设置',
      click: () => {
        mainWindow.show();
        mainWindow.webContents.send('navigate-to', 'settings');
      }
    },
    {
      label: '关于',
      click: () => {
        dialog.showMessageBox(mainWindow, {
          type: 'info',
          title: '关于 Shadowsocks',
          message: 'Shadowsocks Electron Client',
          detail: 'Version 1.0.0\n基于 Electron 开发的 Shadowsocks 客户端'
        });
      }
    },
    { type: 'separator' },
    {
      label: '退出',
      click: () => {
        isQuiting = true;
        app.quit();
      }
    }
  ]);

  tray.setContextMenu(contextMenu);
  tray.setToolTip('Shadowsocks');

  // 双击托盘图标显示主窗口
  tray.on('double-click', () => {
    mainWindow.show();
  });
}

// 创建应用菜单
function createMenu() {
  const template = [
    {
      label: '文件',
      submenu: [
        {
          label: '导入配置',
          accelerator: 'CmdOrCtrl+O',
          click: async () => {
            const result = await dialog.showOpenDialog(mainWindow, {
              properties: ['openFile'],
              filters: [
                { name: 'JSON Files', extensions: ['json'] },
                { name: 'All Files', extensions: ['*'] }
              ]
            });

            if (!result.canceled) {
              mainWindow.webContents.send('import-config', result.filePaths[0]);
            }
          }
        },
        {
          label: '导出配置',
          accelerator: 'CmdOrCtrl+S',
          click: async () => {
            const result = await dialog.showSaveDialog(mainWindow, {
              filters: [
                { name: 'JSON Files', extensions: ['json'] },
                { name: 'All Files', extensions: ['*'] }
              ]
            });

            if (!result.canceled) {
              mainWindow.webContents.send('export-config', result.filePath);
            }
          }
        },
        { type: 'separator' },
        {
          label: '退出',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            isQuiting = true;
            app.quit();
          }
        }
      ]
    },
    {
      label: '编辑',
      submenu: [
        { role: 'undo', label: '撤销' },
        { role: 'redo', label: '重做' },
        { type: 'separator' },
        { role: 'cut', label: '剪切' },
        { role: 'copy', label: '复制' },
        { role: 'paste', label: '粘贴' },
        { role: 'selectall', label: '全选' }
      ]
    },
    {
      label: '视图',
      submenu: [
        { role: 'reload', label: '重新加载' },
        { role: 'forceReload', label: '强制重新加载' },
        { role: 'toggleDevTools', label: '开发者工具' },
        { type: 'separator' },
        { role: 'resetZoom', label: '实际大小' },
        { role: 'zoomIn', label: '放大' },
        { role: 'zoomOut', label: '缩小' },
        { type: 'separator' },
        { role: 'togglefullscreen', label: '全屏' }
      ]
    },
    {
      label: '帮助',
      submenu: [
        {
          label: '关于',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: '关于 Shadowsocks',
              message: 'Shadowsocks Electron Client',
              detail: 'Version 1.0.0\n基于 Electron 开发的 Shadowsocks 客户端'
            });
          }
        },
        {
          label: '项目主页',
          click: () => {
            shell.openExternal('https://github.com/shadowsocks/shadowsocks');
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// 应用准备就绪
app.whenReady().then(() => {
  createMainWindow();
  createTray();
  createMenu();

  // 初始化代理管理器
  proxyManager = new ProxyManager();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createMainWindow();
    }
  });
});

// 所有窗口关闭时退出应用
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// 应用退出前清理
app.on('before-quit', () => {
  isQuiting = true;
});

// IPC 通信处理
ipcMain.handle('get-config', () => {
  return store.get('config', {});
});

ipcMain.handle('set-config', (event, config) => {
  store.set('config', config);
  return true;
});

ipcMain.handle('get-servers', () => {
  return store.get('servers', []);
});

ipcMain.handle('set-servers', (event, servers) => {
  store.set('servers', servers);
  return true;
});

// 更新托盘状态
ipcMain.on('update-tray-status', (event, status) => {
  if (tray) {
    const menu = tray.getContextMenu();
    const connectedItem = menu.getMenuItemById('connected');
    const disconnectedItem = menu.getMenuItemById('disconnected');
    
    if (status === 'connected') {
      connectedItem.checked = true;
      disconnectedItem.checked = false;
      tray.setToolTip('Shadowsocks - 已连接');
    } else {
      connectedItem.checked = false;
      disconnectedItem.checked = true;
      tray.setToolTip('Shadowsocks - 已断开');
    }
  }
});

// 显示通知
ipcMain.on('show-notification', (event, title, body) => {
  new Notification({
    title: title,
    body: body,
    icon: path.join(__dirname, '../../assets/icon.png')
  }).show();
});

// Shadowsocks 连接
ipcMain.handle('connect-shadowsocks', async (event, serverConfig) => {
  try {
    if (shadowsocksClient) {
      await shadowsocksClient.stop();
    }

    const config = store.get('config', {});
    shadowsocksClient = new ShadowsocksClient({
      serverHost: serverConfig.host,
      serverPort: serverConfig.port,
      password: serverConfig.password,
      method: serverConfig.method,
      localPort: config.localPort || 1080
    });

    // 设置事件监听器
    shadowsocksClient.on('error', (error) => {
      mainWindow.webContents.send('shadowsocks-error', error.message);
    });

    shadowsocksClient.on('stats', (stats) => {
      mainWindow.webContents.send('shadowsocks-stats', stats);
    });

    await shadowsocksClient.start();

    // 如果启用了自动代理，设置系统代理
    if (config.autoProxy && proxyManager) {
      await proxyManager.enableProxy(config.localPort || 1080);
    }

    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// Shadowsocks 断开连接
ipcMain.handle('disconnect-shadowsocks', async () => {
  try {
    if (shadowsocksClient) {
      await shadowsocksClient.stop();
      shadowsocksClient = null;
    }

    // 禁用系统代理
    if (proxyManager) {
      await proxyManager.disableProxy();
    }

    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// 获取连接状态
ipcMain.handle('get-connection-status', () => {
  return {
    connected: shadowsocksClient ? shadowsocksClient.isRunning : false,
    stats: shadowsocksClient ? shadowsocksClient.getStats() : null
  };
});

// 测试服务器连接
ipcMain.handle('test-server', async (event, serverConfig) => {
  try {
    const net = require('net');
    return new Promise((resolve) => {
      const socket = new net.Socket();
      const timeout = setTimeout(() => {
        socket.destroy();
        resolve({ success: false, error: '连接超时' });
      }, 5000);

      socket.connect(serverConfig.port, serverConfig.host, () => {
        clearTimeout(timeout);
        socket.destroy();
        resolve({ success: true, latency: Date.now() - startTime });
      });

      const startTime = Date.now();
      socket.on('error', (error) => {
        clearTimeout(timeout);
        resolve({ success: false, error: error.message });
      });
    });
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// 获取代理状态
ipcMain.handle('get-proxy-status', async () => {
  try {
    if (proxyManager) {
      const status = await proxyManager.getProxyStatus();
      const settings = await proxyManager.getCurrentProxySettings();
      return { enabled: status, settings };
    }
    return { enabled: false, settings: {} };
  } catch (error) {
    return { enabled: false, settings: {}, error: error.message };
  }
});
