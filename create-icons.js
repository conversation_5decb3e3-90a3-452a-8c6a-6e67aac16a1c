const fs = require('fs');
const path = require('path');

// 创建简单的 SVG 图标
function createSVGIcon(size, text = 'SS') {
    return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <rect width="${size}" height="${size}" fill="#667eea" rx="8"/>
  <text x="${size/2}" y="${size/2 + size/8}" font-family="Arial, sans-serif" font-size="${size/3}" 
        font-weight="bold" text-anchor="middle" fill="white">${text}</text>
</svg>`;
}

// 创建简单的 ICO 文件头（16x16 像素）
function createSimpleICO() {
    // 这是一个非常简化的 ICO 文件，实际项目中应该使用专门的工具
    const header = Buffer.alloc(22);
    header.writeUInt16LE(0, 0);      // Reserved
    header.writeUInt16LE(1, 2);      // Type (1 = ICO)
    header.writeUInt16LE(1, 4);      // Number of images
    header.writeUInt8(16, 6);        // Width
    header.writeUInt8(16, 7);        // Height
    header.writeUInt8(0, 8);         // Color palette
    header.writeUInt8(0, 9);         // Reserved
    header.writeUInt16LE(1, 10);     // Color planes
    header.writeUInt16LE(32, 12);    // Bits per pixel
    header.writeUInt32LE(0, 14);     // Image size (will be updated)
    header.writeUInt32LE(22, 18);    // Image offset
    
    return header;
}

// 确保 assets 目录存在
const assetsDir = path.join(__dirname, 'assets');
if (!fs.existsSync(assetsDir)) {
    fs.mkdirSync(assetsDir, { recursive: true });
}

// 创建 SVG 图标文件
const iconSVG = createSVGIcon(256, 'SS');
fs.writeFileSync(path.join(assetsDir, 'icon.svg'), iconSVG);

const trayIconSVG = createSVGIcon(32, 'S');
fs.writeFileSync(path.join(assetsDir, 'tray-icon.svg'), trayIconSVG);

// 创建简单的 PNG 占位符（实际上是文本文件，但足够让应用运行）
const pngPlaceholder = `# PNG 图标占位符
# 这是一个占位符文件
# 在生产环境中，请替换为真实的 PNG 图标文件
# 建议尺寸：
# - icon.png: 256x256 像素
# - tray-icon.png: 32x32 像素
`;

fs.writeFileSync(path.join(assetsDir, 'icon.png'), pngPlaceholder);
fs.writeFileSync(path.join(assetsDir, 'tray-icon.png'), pngPlaceholder);

// 创建 ICO 占位符
const icoPlaceholder = `# ICO 图标占位符
# 这是一个占位符文件
# 在生产环境中，请替换为真实的 ICO 图标文件
# 用于 Windows 应用程序图标
`;

fs.writeFileSync(path.join(assetsDir, 'icon.ico'), icoPlaceholder);

console.log('图标文件创建完成！');
console.log('注意：这些是占位符文件，在生产环境中请替换为真实的图标文件。');
console.log('');
console.log('建议的图标规格：');
console.log('- icon.png: 256x256 像素，应用主图标');
console.log('- tray-icon.png: 32x32 像素，系统托盘图标');
console.log('- icon.ico: 多尺寸 ICO 文件，Windows 应用图标');
console.log('');
console.log('您可以从以下网站获取免费图标：');
console.log('- https://www.flaticon.com');
console.log('- https://icons8.com');
console.log('- https://www.iconfinder.com');
