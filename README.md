# Shadowsocks Electron Client

一个基于 Electron 开发的 Windows Shadowsocks 客户端，提供现代化的用户界面和完整的代理功能。

## 功能特性

- ✅ 现代化的用户界面
- ✅ 支持多种加密方式 (AES-GCM, AES-CFB, ChaCha20-Poly1305)
- ✅ 自动系统代理设置
- ✅ 服务器管理和测试
- ✅ 实时流量统计
- ✅ 系统托盘支持
- ✅ 配置导入/导出
- ✅ 连接日志记录

## 项目结构

```
VPN/
├── src/
│   ├── main/           # 主进程代码
│   │   └── main.js     # Electron 主进程
│   ├── renderer/       # 渲染进程代码
│   │   ├── index.html  # 主界面
│   │   ├── styles.css  # 样式文件
│   │   └── renderer.js # 渲染进程逻辑
│   └── common/         # 共享模块
│       ├── shadowsocks.js  # Shadowsocks 核心功能
│       └── proxy.js        # 系统代理管理
├── assets/             # 资源文件
│   ├── icon.png        # 应用图标
│   ├── icon.ico        # Windows 图标
│   └── tray-icon.png   # 托盘图标
├── package.json        # 项目配置
└── README.md          # 说明文档
```

## 安装和运行

### 1. 安装依赖

```bash
npm install
```

### 2. 添加图标文件

在 `assets/` 目录下添加以下图标文件：
- `icon.png` - 应用主图标 (256x256 像素)
- `icon.ico` - Windows 图标文件
- `tray-icon.png` - 系统托盘图标 (16x16 或 32x32 像素)

### 3. 运行应用

```bash
# 开发模式
npm start

# 或者
npm run dev
```

### 4. 打包应用

```bash
# 构建 Windows 安装包
npm run build:win

# 仅打包不创建安装程序
npm run pack
```

## 使用说明

### 1. 添加服务器

1. 点击侧边栏的"服务器"
2. 点击"添加服务器"按钮
3. 填写服务器信息：
   - 服务器名称
   - 服务器地址
   - 端口
   - 密码
   - 加密方式

### 2. 连接服务器

1. 在服务器列表中选择一个服务器
2. 返回"仪表板"页面
3. 点击"连接"按钮

### 3. 系统代理设置

1. 进入"设置"页面
2. 勾选"自动设置系统代理"
3. 配置本地端口（默认 1080）

## 支持的加密方式

- aes-256-gcm
- aes-192-gcm
- aes-128-gcm
- aes-256-cfb
- aes-192-cfb
- aes-128-cfb
- chacha20-ietf-poly1305

## 开发说明

### 主要技术栈

- **Electron**: 跨平台桌面应用框架
- **Node.js**: 后端运行时
- **HTML/CSS/JavaScript**: 前端界面
- **electron-store**: 配置存储
- **socks**: SOCKS 代理支持

### 核心模块

1. **主进程 (main.js)**
   - 窗口管理
   - 系统托盘
   - 菜单处理
   - IPC 通信

2. **Shadowsocks 客户端 (shadowsocks.js)**
   - SOCKS5 代理服务器
   - 加密/解密处理
   - 流量统计

3. **代理管理器 (proxy.js)**
   - Windows 系统代理设置
   - 注册表操作
   - 代理状态检测

### 添加新功能

1. 在相应的模块中添加功能代码
2. 在主进程中添加 IPC 处理
3. 在渲染进程中添加 UI 交互
4. 更新样式文件

## 故障排除

### 常见问题

1. **应用无法启动**
   - 检查 Node.js 版本 (建议 16+)
   - 确保所有依赖已安装
   - 检查图标文件是否存在

2. **连接失败**
   - 验证服务器信息是否正确
   - 检查网络连接
   - 查看日志页面的错误信息

3. **系统代理不生效**
   - 确保以管理员权限运行
   - 检查 Windows 代理设置
   - 重启浏览器

### 日志查看

应用内置了日志功能，可以在"日志"页面查看详细的运行信息和错误消息。

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 注意事项

- 本项目仅供学习和研究使用
- 请遵守当地法律法规
- 使用前请确保拥有合法的 Shadowsocks 服务器
