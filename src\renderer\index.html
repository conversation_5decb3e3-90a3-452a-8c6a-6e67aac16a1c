<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shadowsocks</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="app">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="logo">
                <img src="../../assets/icon.png" alt="Shadowsocks" class="logo-img">
                <h1>Shadowsocks</h1>
            </div>
            
            <nav class="nav">
                <ul>
                    <li><a href="#" class="nav-link active" data-page="dashboard">仪表板</a></li>
                    <li><a href="#" class="nav-link" data-page="servers">服务器</a></li>
                    <li><a href="#" class="nav-link" data-page="settings">设置</a></li>
                    <li><a href="#" class="nav-link" data-page="logs">日志</a></li>
                </ul>
            </nav>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 仪表板页面 -->
            <div id="dashboard" class="page active">
                <div class="page-header">
                    <h2>仪表板</h2>
                </div>
                
                <div class="status-card">
                    <div class="status-indicator">
                        <div class="status-dot disconnected" id="statusDot"></div>
                        <span class="status-text" id="statusText">未连接</span>
                    </div>
                    
                    <div class="connection-controls">
                        <button class="btn btn-primary" id="connectBtn">连接</button>
                        <button class="btn btn-secondary" id="disconnectBtn" disabled>断开</button>
                    </div>
                </div>

                <div class="server-info">
                    <h3>当前服务器</h3>
                    <div class="server-details" id="currentServer">
                        <p>未选择服务器</p>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <h4>上传流量</h4>
                        <span class="stat-value" id="uploadStats">0 B</span>
                    </div>
                    <div class="stat-card">
                        <h4>下载流量</h4>
                        <span class="stat-value" id="downloadStats">0 B</span>
                    </div>
                    <div class="stat-card">
                        <h4>连接时长</h4>
                        <span class="stat-value" id="connectionTime">00:00:00</span>
                    </div>
                    <div class="stat-card">
                        <h4>延迟</h4>
                        <span class="stat-value" id="latency">-- ms</span>
                    </div>
                </div>
            </div>

            <!-- 服务器页面 -->
            <div id="servers" class="page">
                <div class="page-header">
                    <h2>服务器管理</h2>
                    <button class="btn btn-primary" id="addServerBtn">添加服务器</button>
                </div>
                
                <div class="servers-list" id="serversList">
                    <!-- 服务器列表将在这里动态生成 -->
                </div>
            </div>

            <!-- 设置页面 -->
            <div id="settings" class="page">
                <div class="page-header">
                    <h2>设置</h2>
                </div>
                
                <div class="settings-section">
                    <h3>代理设置</h3>
                    <div class="setting-item">
                        <label>
                            <input type="checkbox" id="autoProxy"> 自动设置系统代理
                        </label>
                    </div>
                    <div class="setting-item">
                        <label>本地端口:</label>
                        <input type="number" id="localPort" value="1080" min="1024" max="65535">
                    </div>
                </div>

                <div class="settings-section">
                    <h3>启动设置</h3>
                    <div class="setting-item">
                        <label>
                            <input type="checkbox" id="autoStart"> 开机自启动
                        </label>
                    </div>
                    <div class="setting-item">
                        <label>
                            <input type="checkbox" id="startMinimized"> 启动时最小化到托盘
                        </label>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>配置管理</h3>
                    <div class="setting-buttons">
                        <button class="btn btn-secondary" id="importConfigBtn">导入配置</button>
                        <button class="btn btn-secondary" id="exportConfigBtn">导出配置</button>
                        <button class="btn btn-danger" id="resetConfigBtn">重置配置</button>
                    </div>
                </div>
            </div>

            <!-- 日志页面 -->
            <div id="logs" class="page">
                <div class="page-header">
                    <h2>日志</h2>
                    <div class="log-controls">
                        <button class="btn btn-secondary" id="clearLogsBtn">清空日志</button>
                        <button class="btn btn-secondary" id="exportLogsBtn">导出日志</button>
                    </div>
                </div>

                <div class="logs-container">
                    <div class="log-output" id="logOutput">
                        <!-- 日志内容将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加服务器模态框 -->
    <div id="addServerModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>添加服务器</h3>
                <span class="close" id="closeModal">&times;</span>
            </div>
            <div class="modal-body">
                <form id="serverForm">
                    <div class="form-group">
                        <label for="serverName">服务器名称:</label>
                        <input type="text" id="serverName" name="serverName" required>
                    </div>
                    <div class="form-group">
                        <label for="serverHost">服务器地址:</label>
                        <input type="text" id="serverHost" name="serverHost" required>
                    </div>
                    <div class="form-group">
                        <label for="serverPort">端口:</label>
                        <input type="number" id="serverPort" name="serverPort" min="1" max="65535" required>
                    </div>
                    <div class="form-group">
                        <label for="serverPassword">密码:</label>
                        <input type="password" id="serverPassword" name="serverPassword" required>
                    </div>
                    <div class="form-group">
                        <label for="serverMethod">加密方式:</label>
                        <select id="serverMethod" name="serverMethod" required>
                            <option value="aes-256-gcm">aes-256-gcm</option>
                            <option value="aes-128-gcm">aes-128-gcm</option>
                            <option value="chacha20-ietf-poly1305">chacha20-ietf-poly1305</option>
                            <option value="aes-256-cfb">aes-256-cfb</option>
                            <option value="aes-128-cfb">aes-128-cfb</option>
                        </select>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">添加</button>
                        <button type="button" class="btn btn-secondary" id="cancelAdd">取消</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="renderer.js"></script>
</body>
</html>
