# Shadowsocks Electron 客户端 - 部署指南

## 项目概述

这是一个完整的基于 Electron 的 Windows Shadowsocks 客户端，具有现代化的用户界面和完整的代理功能。

## 已完成的功能

### ✅ 核心功能
- [x] Electron 应用框架
- [x] 现代化用户界面（HTML/CSS/JavaScript）
- [x] Shadowsocks 客户端核心功能
- [x] SOCKS5 本地代理服务器
- [x] 多种加密方式支持
- [x] Windows 系统代理自动设置
- [x] 服务器管理（添加/删除/选择）
- [x] 配置存储和管理
- [x] 系统托盘支持
- [x] 实时流量统计
- [x] 连接日志记录
- [x] 服务器连接测试

### ✅ 用户界面
- [x] 仪表板（连接状态、统计信息）
- [x] 服务器管理页面
- [x] 设置页面
- [x] 日志页面
- [x] 添加服务器模态框
- [x] 响应式设计
- [x] 现代化样式

### ✅ 系统集成
- [x] 系统托盘图标和菜单
- [x] 应用菜单
- [x] 窗口管理
- [x] 配置文件导入/导出
- [x] 开机自启动选项

## 项目结构

```
VPN/
├── src/
│   ├── main/
│   │   └── main.js              # Electron 主进程
│   ├── renderer/
│   │   ├── index.html           # 主界面
│   │   ├── styles.css           # 样式文件
│   │   └── renderer.js          # 渲染进程逻辑
│   └── common/
│       ├── shadowsocks.js       # Shadowsocks 核心功能
│       └── proxy.js             # 系统代理管理
├── assets/                      # 资源文件
├── scripts/                     # 辅助脚本
├── package.json                 # 项目配置
├── README.md                    # 使用说明
├── DEPLOYMENT.md               # 部署指南
├── create-icons.js             # 图标创建脚本
└── test-app.js                 # 测试脚本
```

## 技术栈

- **前端**: HTML5, CSS3, JavaScript (ES6+)
- **后端**: Node.js, Electron
- **加密**: Node.js crypto 模块
- **网络**: Node.js net 模块, socks 库
- **存储**: electron-store
- **UI框架**: 原生 HTML/CSS（现代化设计）

## 支持的加密方式

- aes-256-gcm
- aes-192-gcm  
- aes-128-gcm
- aes-256-cfb
- aes-192-cfb
- aes-128-cfb
- chacha20-ietf-poly1305

## 安装和运行

### 1. 环境要求
- Node.js 16+ 
- npm 或 yarn
- Windows 10/11

### 2. 安装依赖
```bash
npm install
```

### 3. 运行应用
```bash
# 开发模式
npm start

# 或者
npm run dev
```

### 4. 构建应用
```bash
# 构建 Windows 安装包
npm run build:win

# 仅打包不创建安装程序
npm run pack
```

## 使用指南

### 1. 添加服务器
1. 启动应用
2. 点击侧边栏"服务器"
3. 点击"添加服务器"
4. 填写服务器信息：
   - 服务器名称
   - 服务器地址
   - 端口
   - 密码
   - 加密方式

### 2. 连接服务器
1. 在服务器列表中选择服务器
2. 返回"仪表板"
3. 点击"连接"按钮

### 3. 配置系统代理
1. 进入"设置"页面
2. 勾选"自动设置系统代理"
3. 配置本地端口（默认1080）

## 开发说明

### 主要模块

1. **主进程 (main.js)**
   - 窗口管理和生命周期
   - 系统托盘和菜单
   - IPC 通信处理
   - 文件操作

2. **Shadowsocks 客户端 (shadowsocks.js)**
   - SOCKS5 代理服务器实现
   - 加密/解密处理
   - 连接管理
   - 流量统计

3. **代理管理器 (proxy.js)**
   - Windows 注册表操作
   - 系统代理设置
   - 代理状态检测

4. **渲染进程 (renderer.js)**
   - UI 交互逻辑
   - 状态管理
   - 事件处理

### 扩展功能

如需添加新功能：
1. 在相应模块中实现核心逻辑
2. 在主进程中添加 IPC 处理
3. 在渲染进程中添加 UI 交互
4. 更新样式和界面

## 故障排除

### 常见问题

1. **应用无法启动**
   - 检查 Node.js 版本
   - 运行 `npm install` 重新安装依赖
   - 检查防火墙设置

2. **连接失败**
   - 验证服务器信息
   - 检查网络连接
   - 查看日志页面错误信息

3. **系统代理不生效**
   - 以管理员权限运行
   - 检查 Windows 代理设置
   - 重启浏览器

### 调试

- 按 `Ctrl+Shift+I` 打开开发者工具
- 查看控制台错误信息
- 检查网络请求
- 查看应用内日志

## 安全注意事项

- 本项目仅供学习和研究使用
- 请遵守当地法律法规
- 确保使用合法的 Shadowsocks 服务器
- 定期更新依赖包以修复安全漏洞

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request 来改进项目。

## 联系方式

如有问题或建议，请通过 GitHub Issues 联系。
